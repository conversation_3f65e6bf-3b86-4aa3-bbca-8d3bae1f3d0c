//go:build linux && headless
// +build linux,headless

package common

import (
	"bytes"
	"encoding/json"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// ScreenshotRequest 截图请求
type ScreenshotRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	MonitorID    int    `json:"monitor_id"`    // 显示器ID (多显示器支持)
	MonitorIndex int    `json:"monitor_index"` // 显示器索引 (前端兼容)
	X            int    `json:"x"`             // 区域截图的X坐标
	Y            int    `json:"y"`             // 区域截图的Y坐标
	Width        int    `json:"width"`         // 区域截图的宽度
	Height       int    `json:"height"`        // 区域截图的高度
	Format       string `json:"format"`        // 图片格式: "png", "jpeg", "bmp"
	Quality      int    `json:"quality"`       // JPEG质量 (1-100)
}

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	TaskID    uint64 `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 是否成功
	Error     string `json:"error"`      // 错误信息
	ImageData []byte `json:"image_data"` // 图片数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 文件大小
	Timestamp int64  `json:"timestamp"`  // 截图时间戳
}

// 显示器信息结构体
type DisplayInfo struct {
	ID        int    `json:"id"`         // 显示器ID
	Name      string `json:"name"`       // 显示器名称
	X         int    `json:"x"`          // X坐标
	Y         int    `json:"y"`          // Y坐标
	Width     int    `json:"width"`      // 宽度
	Height    int    `json:"height"`     // 高度
	IsPrimary bool   `json:"is_primary"` // 是否主显示器
}

// MonitorInfo 显示器信息 (与Windows版本保持一致)
type MonitorInfo struct {
	Index   int  `json:"index"`   // 显示器索引
	X       int  `json:"x"`       // X坐标
	Y       int  `json:"y"`       // Y坐标
	Width   int  `json:"width"`   // 宽度
	Height  int  `json:"height"`  // 高度
	Primary bool `json:"primary"` // 是否主显示器
}

// 扩展的ScreenshotRequest结构体，添加流相关字段
type ExtendedScreenshotRequest struct {
	ScreenshotRequest
	FPS                      int  `json:"fps,omitempty"`                        // 帧率
	EnableDiffDetection      bool `json:"enable_diff_detection,omitempty"`      // 启用差异检测
	DiffThreshold            int  `json:"diff_threshold,omitempty"`             // 差异阈值百分比
	CPULimit                 int  `json:"cpu_limit,omitempty"`                  // CPU使用限制百分比
	BandwidthLimit           int  `json:"bandwidth_limit,omitempty"`            // 带宽限制 KB/s
	EnableMemoryOptimization bool `json:"enable_memory_optimization,omitempty"` // 启用内存优化
}

type MonitorListRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
}

// MonitorListResponse 显示器列表响应
type MonitorListResponse struct {
	TaskID   uint64        `json:"task_id"`  // 任务ID
	Success  bool          `json:"success"`  // 操作是否成功
	Monitors []MonitorInfo `json:"monitors"` // 显示器列表
	Error    string        `json:"error"`    // 错误信息
	Count    int           `json:"count"`    // 显示器数量
}

// ScreenStreamStartResponse 屏幕流开始响应
type ScreenStreamStartResponse struct {
	TaskID   uint64 `json:"task_id"`   // 任务ID
	Success  bool   `json:"success"`   // 操作是否成功
	Error    string `json:"error"`     // 错误信息
	StreamID string `json:"stream_id"` // 流ID
}

// ScreenStreamStopRequest 屏幕流停止请求
type ScreenStreamStopRequest struct {
	TaskID   uint64 `json:"task_id"`   // 任务ID
	StreamID string `json:"stream_id"` // 流ID
}

// ScreenStreamStopResponse 屏幕流停止响应
type ScreenStreamStopResponse struct {
	TaskID  uint64 `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	Error   string `json:"error"`   // 错误信息
}

// 无头环境下的截图实现 - 使用命令行工具
func captureScreen(req *ScreenshotRequest) ([]byte, int, int, error) {
	// 尝试使用不同的截图工具
	screenshotTools := []string{
		"scrot",            // 最常用的Linux截图工具
		"import",           // ImageMagick的截图工具
		"gnome-screenshot", // GNOME截图工具
		"xwd",              // X Window Dump
	}

	var imageData []byte
	var width, height int = 1920, 1080 // 默认尺寸
	var err error

	// 根据请求类型调整参数
	switch req.Type {
	case 0: // 全屏截图
		width, height = 1920, 1080
	case 1: // 活动窗口截图
		width, height = 800, 600
	case 2: // 区域截图
		if req.X > 0 && req.Y > 0 && req.Width > 0 && req.Height > 0 {
			width, height = int(req.Width), int(req.Height)
		}
	case 3: // 指定显示器截图
		width, height = 1920, 1080
	}

	// 尝试每个截图工具
	for _, tool := range screenshotTools {
		imageData, err = tryScreenshotTool(tool, req, width, height)
		if err == nil && len(imageData) > 0 {
			log.Printf("✅ 使用 %s 截图成功", tool)
			return imageData, width, height, nil
		}
		log.Printf("❌ %s 截图失败: %v", tool, err)
	}

	// 所有工具都失败，生成占位图片
	log.Printf("⚠️ 所有截图工具都不可用，生成占位图片")
	return generatePlaceholderImage(width, height, req.Format, req.Quality)
}

// 尝试使用指定的截图工具
func tryScreenshotTool(tool string, req *ScreenshotRequest, width, height int) ([]byte, error) {
	// 创建临时文件
	tempDir := "/tmp"
	tempFile := filepath.Join(tempDir, fmt.Sprintf("screenshot_%d.png", time.Now().UnixNano()))
	defer os.Remove(tempFile) // 清理临时文件

	var cmd *exec.Cmd

	switch tool {
	case "scrot":
		// scrot 是最常用的Linux截图工具
		args := []string{tempFile}
		if req.Type == 2 && req.Width > 0 && req.Height > 0 {
			// 区域截图
			args = append([]string{"-a", fmt.Sprintf("%d,%d,%d,%d", req.X, req.Y, req.Width, req.Height)}, args...)
		}
		cmd = exec.Command("scrot", args...)

	case "import":
		// ImageMagick 的 import 工具
		args := []string{"-window", "root", tempFile}
		if req.Type == 2 && req.Width > 0 && req.Height > 0 {
			// 区域截图
			args = []string{"-crop", fmt.Sprintf("%dx%d+%d+%d", req.Width, req.Height, req.X, req.Y), "-window", "root", tempFile}
		}
		cmd = exec.Command("import", args...)

	case "gnome-screenshot":
		// GNOME 截图工具
		args := []string{"-f", tempFile}
		if req.Type == 2 && req.Width > 0 && req.Height > 0 {
			// gnome-screenshot 不支持区域截图参数，跳过
			return nil, fmt.Errorf("gnome-screenshot 不支持区域截图")
		}
		cmd = exec.Command("gnome-screenshot", args...)

	case "xwd":
		// X Window Dump
		args := []string{"-root", "-out", tempFile}
		cmd = exec.Command("xwd", args...)

	default:
		return nil, fmt.Errorf("不支持的截图工具: %s", tool)
	}

	// 设置环境变量，尝试连接到显示服务器
	cmd.Env = append(os.Environ(),
		"DISPLAY=:0",
		"XAUTHORITY=/home/<USER>/.Xauthority", // 可能需要调整路径
	)

	// 执行命令
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("执行 %s 失败: %v", tool, err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(tempFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("截图文件未生成: %s", tempFile)
	}

	// 读取截图文件
	imageData, err := ioutil.ReadFile(tempFile)
	if err != nil {
		return nil, fmt.Errorf("读取截图文件失败: %v", err)
	}

	// 如果需要转换格式
	if req.Format != "" && req.Format != "png" {
		return convertImageFormat(imageData, req.Format, req.Quality)
	}

	return imageData, nil
}

// 生成占位图片
func generatePlaceholderImage(width, height int, format string, quality int) ([]byte, int, int, error) {
	// 创建一个简单的占位图片
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 填充背景色（深灰色）
	bgColor := color.RGBA{64, 64, 64, 255}
	draw.Draw(img, img.Bounds(), &image.Uniform{bgColor}, image.Point{}, draw.Src)

	// 添加一些简单的文本指示
	drawSimpleText(img, "No Display Available", width/2-100, height/2-20)
	drawSimpleText(img, "Headless Environment", width/2-110, height/2)
	drawSimpleText(img, "Screenshot tools not available", width/2-130, height/2+20)

	// 编码图片
	imageData, err := encodeImage(img, format, quality)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
	}

	return imageData, width, height, nil
}

// 转换图片格式
func convertImageFormat(imageData []byte, format string, quality int) ([]byte, error) {
	// 解码原始图片
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("解码图片失败: %v", err)
	}

	// 重新编码为指定格式
	return encodeImage(img, format, quality)
}

// 简单的像素文字绘制函数
func drawSimpleText(img *image.RGBA, text string, x, y int) {
	textColor := color.RGBA{255, 255, 255, 255} // 白色文字

	// 简单的像素字体 - 每个字符8x8像素
	for i, char := range text {
		drawChar(img, char, x+i*9, y, textColor)
	}
}

// 绘制单个字符（简化版本，只支持基本字符）
func drawChar(img *image.RGBA, char rune, x, y int, c color.RGBA) {
	// 简单的8x8像素字体模式
	var pattern [][]bool

	switch char {
	case 'N':
		pattern = [][]bool{
			{true, false, false, false, false, false, true, false},
			{true, true, false, false, false, false, true, false},
			{true, false, true, false, false, false, true, false},
			{true, false, false, true, false, false, true, false},
			{true, false, false, false, true, false, true, false},
			{true, false, false, false, false, true, true, false},
			{true, false, false, false, false, false, true, false},
			{false, false, false, false, false, false, false, false},
		}
	case 'o':
		pattern = [][]bool{
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, true, true, true, true, false, false, false},
			{true, false, false, false, false, true, false, false},
			{true, false, false, false, false, true, false, false},
			{false, true, true, true, true, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
		}
	case ' ':
		pattern = [][]bool{
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
			{false, false, false, false, false, false, false, false},
		}
	default:
		// 默认显示一个简单的方块
		pattern = [][]bool{
			{true, true, true, true, true, true, true, false},
			{true, false, false, false, false, false, true, false},
			{true, false, false, false, false, false, true, false},
			{true, false, false, false, false, false, true, false},
			{true, false, false, false, false, false, true, false},
			{true, false, false, false, false, false, true, false},
			{true, true, true, true, true, true, true, false},
			{false, false, false, false, false, false, false, false},
		}
	}

	// 绘制字符
	for row := 0; row < len(pattern) && row < 8; row++ {
		for col := 0; col < len(pattern[row]) && col < 8; col++ {
			if pattern[row][col] {
				px := x + col
				py := y + row
				if px >= 0 && py >= 0 && px < img.Bounds().Max.X && py < img.Bounds().Max.Y {
					img.Set(px, py, c)
				}
			}
		}
	}
}

// 检查是否支持截图功能
func isScreenshotSupported() bool {
	// 检查是否有可用的截图工具
	screenshotTools := []string{"scrot", "import", "gnome-screenshot", "xwd"}

	for _, tool := range screenshotTools {
		if _, err := exec.LookPath(tool); err == nil {
			log.Printf("✅ 找到截图工具: %s", tool)
			return true
		}
	}

	log.Printf("❌ 未找到任何截图工具")
	return false
}

// 获取显示器信息
func getDisplayInfo() ([]DisplayInfo, error) {
	return []DisplayInfo{
		{
			ID:        0,
			Name:      "Virtual Display",
			Width:     1920,
			Height:    1080,
			X:         0,
			Y:         0,
			IsPrimary: true,
		},
	}, nil
}

// 图片编码函数 - 无头环境版本
func encodeImage(img image.Image, format string, quality int) ([]byte, error) {
	var buf bytes.Buffer

	switch strings.ToLower(format) {
	case "png", "":
		err := png.Encode(&buf, img)
		if err != nil {
			return nil, fmt.Errorf("PNG编码失败: %v", err)
		}
	case "jpeg", "jpg":
		if quality <= 0 || quality > 100 {
			quality = 80 // 默认质量
		}
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, fmt.Errorf("JPEG编码失败: %v", err)
		}
	default:
		return nil, fmt.Errorf("不支持的图片格式: %s", format)
	}

	return buf.Bytes(), nil
}

// 获取显示器信息 - 无头环境版本
func getMonitorInfo() []MonitorInfo {
	return []MonitorInfo{
		{
			Index:   0,
			X:       0,
			Y:       0,
			Width:   1920,
			Height:  1080,
			Primary: true,
		},
	}
}

// 处理截图请求 - 无头环境版本
func (cm *ConnectionManager) handleScreenshotRequest(packet *Packet) {
	// 创建错误响应结构体
	errorResp := ScreenshotResponse{
		TaskID:  0,
		Success: false,
		Error:   "无头环境不支持截图",
	}

	var req ScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析截图请求失败: %v", err)
		errorResp.Error = "解析请求失败: " + err.Error()
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID
	log.Printf("🖼️ 收到截图请求但运行在无头环境 - 类型: %d, 格式: %s", req.Type, req.Format)

	// 生成占位图片
	imageData, width, height, err := captureScreen(&req)
	if err != nil {
		log.Printf("❌ 生成占位图片失败: %v", err)
		errorResp.Error = "生成占位图片失败: " + err.Error()
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})

	log.Printf("✅ 已发送占位截图 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(imageData))
}

// 处理显示器列表请求 - 无头环境版本
func (cm *ConnectionManager) handleMonitorListRequest(packet *Packet) {
	var req MonitorListRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析显示器列表请求失败: %v", err)
		cm.sendResp(Screenshot, MonitorList, MonitorListResponse{
			TaskID:  0,
			Success: false,
			Error:   "解析请求失败: " + err.Error(),
		})
		return
	}

	log.Printf("🖥️ 收到显示器列表请求 - 无头环境")

	monitors := getMonitorInfo()
	cm.sendResp(Screenshot, MonitorList, MonitorListResponse{
		TaskID:   req.TaskID,
		Success:  true,
		Monitors: monitors,
		Count:    len(monitors),
	})

	log.Printf("✅ 已发送虚拟显示器列表 - 数量: %d", len(monitors))
}

// 计算图像差异百分比 - 无头环境版本（简化实现）
func (cm *ConnectionManager) calculateImageDifference(img1, img2 []byte) float64 {
	if len(img1) != len(img2) {
		return 100.0 // 完全不同
	}

	if len(img1) == 0 {
		return 0.0
	}

	// 简单的字节比较
	diffCount := 0
	for i := 0; i < len(img1); i++ {
		if img1[i] != img2[i] {
			diffCount++
		}
	}

	return float64(diffCount) / float64(len(img1)) * 100.0
}

// 处理屏幕流开始请求 - 无头环境版本
func (cm *ConnectionManager) handleScreenStreamStart(packet *Packet) {
	var req ExtendedScreenshotRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流开始请求失败: %v", err)
		cm.sendResp(Screenshot, StreamStart, ScreenStreamStartResponse{
			TaskID:  0,
			Success: false,
			Error:   "解析请求失败: " + err.Error(),
		})
		return
	}

	log.Printf("📺 收到屏幕流开始请求但运行在无头环境")

	cm.sendResp(Screenshot, StreamStart, ScreenStreamStartResponse{
		TaskID:   req.TaskID,
		Success:  false,
		Error:    "无头环境不支持屏幕流",
		StreamID: "",
	})
}

// 处理屏幕流停止请求 - 无头环境版本
func (cm *ConnectionManager) handleScreenStreamStop(packet *Packet) {
	var req ScreenStreamStopRequest
	if err := cm.serializer.Deserialize(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流停止请求失败: %v", err)
		cm.sendResp(Screenshot, StreamStop, ScreenStreamStopResponse{
			TaskID:  0,
			Success: false,
			Error:   "解析请求失败: " + err.Error(),
		})
		return
	}

	log.Printf("📺 收到屏幕流停止请求")

	cm.sendResp(Screenshot, StreamStop, ScreenStreamStopResponse{
		TaskID:  req.TaskID,
		Success: true,
	})
}
