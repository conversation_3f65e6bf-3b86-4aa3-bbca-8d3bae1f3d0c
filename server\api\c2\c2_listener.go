package c2

import (
	"server/global"
	"server/model/request"
	"server/model/response"
	"server/model/sys"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ListenerApi struct{}

// CreateListener 创建监听器
func (l *ListenerApi) CreateListener(c *gin.Context) {
	var listener sys.Listener
	if err := c.ShouldBindJSON(&listener); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 🚨 关键修复：从JWT token中获取当前用户ID
	claims, exists := c.Get("claims")
	if !exists {
		response.NoAuth("未登录或非法访问", c)
		return
	}
	customClaims, ok := claims.(*sys.CustomClaims)
	if !ok {
		response.NoAuth("token解析失败", c)
		return
	}

	// 设置监听器的创建者
	listener.UserID = customClaims.BaseClaims.ID

	if err := listenerService.CreateListener(listener); err != nil {
		global.LOG.Error("创建监听器失败", zap.Error(err))
		response.ErrorWithMessage("创建监听器失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建监听器成功", c)
}

// DeleteListener 删除监听器
func (l *ListenerApi) DeleteListener(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("参数错误", c)
		return
	}

	if err = listenerService.DeleteListener(uint(id)); err != nil {
		global.LOG.Error("删除监听器失败", zap.Error(err))
		response.ErrorWithMessage("删除监听器失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除监听器成功", c)
}

// UpdateListener 更新监听器
func (l *ListenerApi) UpdateListener(c *gin.Context) {
	var listener sys.Listener
	if err := c.ShouldBindJSON(&listener); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if listener.ID == 0 {
		response.ErrorWithMessage("参数错误: 缺少ID", c)
		return
	}

	if err := listenerService.UpdateListener(listener); err != nil {
		global.LOG.Error("更新监听器失败", zap.Error(err))
		response.ErrorWithMessage("更新监听器失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新监听器成功", c)
}

// GetListener 获取单个监听器
func (l *ListenerApi) GetListener(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("参数错误", c)
		return
	}

	listener, err := listenerService.GetListener(uint(id))
	if err != nil {
		global.LOG.Error("获取监听器失败", zap.Error(err))
		response.ErrorWithMessage("获取监听器失败: "+err.Error(), c)
		return
	}

	response.OkWithData(listener, c)
}

// GetListenerList 获取监听器列表
func (l *ListenerApi) GetListenerList(c *gin.Context) {
	var searchInfo request.ListenerSearch
	if err := c.ShouldBindJSON(&searchInfo); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if searchInfo.Page == 0 {
		searchInfo.Page = 1
	}
	if searchInfo.PageSize == 0 {
		searchInfo.PageSize = 10
	}

	listenerInfo := sys.Listener{
		Type:              searchInfo.Type,
		Status:            searchInfo.Status,
		Remark:            searchInfo.Remark,
		LocalListenAddr:   searchInfo.LocalListenAddr,
		RemoteConnectAddr: searchInfo.RemoteConnectAddr,
	}

	list, total, err := listenerService.GetListenerList(listenerInfo, searchInfo.PageSize, searchInfo.Page)
	if err != nil {
		global.LOG.Error("获取监听器列表失败", zap.Error(err))
		response.ErrorWithMessage("获取监听器列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     searchInfo.Page,
		PageSize: searchInfo.PageSize,
	}, "获取监听器列表成功", c)
}

// UpdateListenerStatus 更新监听器状态
func (l *ListenerApi) UpdateListenerStatus(c *gin.Context) {
	var statusChange request.ListenerStatusChange
	if err := c.ShouldBindJSON(&statusChange); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if statusChange.ID == 0 {
		response.ErrorWithMessage("参数错误: 缺少ID", c)
		return
	}

	if err := listenerService.UpdateListenerStatus(statusChange.ID, statusChange.Status); err != nil {
		global.LOG.Error("更新监听器状态失败", zap.Error(err))
		response.ErrorWithMessage("更新监听器状态失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新监听器状态成功", c)
}

// GetOnlineListeners 获取在线监听器列表
func (l *ListenerApi) GetOnlineListeners(c *gin.Context) {
	listenerType := c.Query("type")
	if listenerType == "" {
		response.ErrorWithMessage("参数错误: 缺少监听器类型", c)
		return
	}

	list, err := listenerService.GetOnlineListenersByType(listenerType)
	if err != nil {
		global.LOG.Error("获取在线监听器列表失败", zap.Error(err))
		response.ErrorWithMessage("获取在线监听器列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(list, c)
}

// ConnectForwardClient 连接正向客户端
func (l *ListenerApi) ConnectForwardClient(c *gin.Context) {
	var req request.ForwardConnectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if req.ListenerID == 0 {
		response.ErrorWithMessage("参数错误: 缺少监听器ID", c)
		return
	}

	if req.ClientAddr == "" {
		response.ErrorWithMessage("参数错误: 缺少客户端地址", c)
		return
	}

	if err := listenerService.ConnectForwardClient(req.ListenerID, req.ClientAddr); err != nil {
		global.LOG.Error("连接正向客户端失败", zap.Error(err))
		response.ErrorWithMessage("连接正向客户端失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("连接正向客户端成功", c)
}
