package tcp

import (
	"encoding/json"
	"fmt"
	"server/core/manager/cache"
	"server/core/manager/dbpool"
	"server/global"
	"server/model/basic"
	"server/model/task"
	"server/model/tlv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)



// HandleCronResponse 处理定时任务响应
func (l *TCPListener) HandleCronResponse(clientID uint, packet *tlv.Packet) error {
	global.LOG.Info("Handling cron response", 
		zap.Uint("client_id", clientID),
		zap.Uint8("code", packet.Header.Code))

	switch packet.Header.Code {
	case tlv.CronScreentshot:
		return l.handleCronScreenshotResponse(clientID, packet)
	default:
		return l.handleGeneralCronResponse(clientID, packet)
	}
}

// handleGeneralCronResponse 处理通用定时任务响应
func (l *TCPListener) handleGeneralCronResponse(clientID uint, packet *tlv.Packet) error {
	var responseData CronResponseData
	if err := json.Unmarshal(packet.PacketData.Data, &responseData); err != nil {
		global.LOG.Error("Failed to unmarshal cron response data", zap.Error(err))
		return err
	}

	// 更新任务状态
	err := l.updateCronTaskStatus(responseData.TaskID, responseData.Status, responseData.Message, responseData.Error)
	if err != nil {
		global.LOG.Error("Failed to update cron task status", zap.Error(err))
		return err
	}

	// 如果是执行成功的响应，创建执行记录
	if responseData.Status == "success" && responseData.ExecutionID > 0 {
		err = l.createCronExecutionRecord(responseData.CronJobID, responseData.ExecutionID, responseData.Data)
		if err != nil {
			global.LOG.Error("Failed to create cron execution record", zap.Error(err))
		}
	}

	// 存储响应到缓存
	cache.ResponseMgr.StoreResponse(responseData.TaskID, "cron_task", responseData, responseData.Error)

	global.LOG.Info("Cron task response processed successfully", 
		zap.Uint64("task_id", responseData.TaskID),
		zap.String("status", responseData.Status))

	return nil
}

// handleCronScreenshotResponse 处理定时截图响应
func (l *TCPListener) handleCronScreenshotResponse(clientID uint, packet *tlv.Packet) error {
	var responseData CronScreenshotResponseData
	if err := json.Unmarshal(packet.PacketData.Data, &responseData); err != nil {
		global.LOG.Error("Failed to unmarshal cron screenshot response data", zap.Error(err))
		return err
	}

	// 更新任务状态
	err := l.updateCronTaskStatus(responseData.TaskID, responseData.Status, responseData.Message, responseData.Error)
	if err != nil {
		global.LOG.Error("Failed to update cron screenshot task status", zap.Error(err))
		return err
	}

	// 如果是执行成功的响应，处理截图数据
	if responseData.Status == "success" && responseData.ScreenshotData != nil {
		err = l.processCronScreenshotData(responseData)
		if err != nil {
			global.LOG.Error("Failed to process cron screenshot data", zap.Error(err))
		}
	}

	// 存储响应到缓存
	cache.ResponseMgr.StoreResponse(responseData.TaskID, "cron_screenshot", responseData, responseData.Error)

	global.LOG.Info("Cron screenshot response processed successfully", 
		zap.Uint64("task_id", responseData.TaskID),
		zap.String("status", responseData.Status))

	return nil
}

// updateCronTaskStatus 更新定时任务状态
func (l *TCPListener) updateCronTaskStatus(taskID uint64, status, message, errorMsg string) error {
	updates := map[string]interface{}{
		"status":       status,
		"message":      message,
		"completed_at": time.Now(),
	}

	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	return dbpool.ExecuteDBOperationAsyncAndWait("cron_task_status_update", func(db *gorm.DB) error {
		return db.Model(&task.CronTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

// createCronExecutionRecord 创建定时任务执行记录
func (l *TCPListener) createCronExecutionRecord(cronJobID uint, executionID uint64, data interface{}) error {
	// 获取定时任务信息
	var cronJob basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_get", func(db *gorm.DB) error {
		return db.Where("id = ?", cronJobID).First(&cronJob).Error
	})
	if err != nil {
		return fmt.Errorf("failed to get cron job: %w", err)
	}

	// 创建执行记录
	execution := &basic.CronJobExecution{
		CronJobID: cronJobID,
		TaskID:    executionID,
		Status:    "success",
		StartedAt: &time.Time{}, // 这里应该从客户端获取实际开始时间
		EndedAt:   &time.Time{}, // 这里应该从客户端获取实际结束时间
		Duration:  0,            // 这里应该计算实际执行时间
	}

	// 如果有数据，序列化存储
	if data != nil {
		if dataBytes, err := json.Marshal(data); err == nil {
			execution.Result = string(dataBytes)
		}
	}

	return dbpool.ExecuteDBOperationAsyncAndWait("cron_execution_create", func(db *gorm.DB) error {
		return db.Create(execution).Error
	})
}

// processCronScreenshotData 处理定时截图数据
func (l *TCPListener) processCronScreenshotData(responseData CronScreenshotResponseData) error {
	// 这里可以实现截图数据的处理逻辑
	// 例如：保存截图文件、更新截图记录等
	
	global.LOG.Info("Processing cron screenshot data", 
		zap.Uint64("task_id", responseData.TaskID),
		zap.String("file_path", responseData.FilePath),
		zap.Int64("file_size", responseData.FileSize))

	// 可以在这里添加具体的截图处理逻辑
	// 例如：
	// 1. 保存截图文件到指定目录
	// 2. 更新截图记录表
	// 3. 触发后续处理流程等

	return nil
}

// GetCronTaskResponse 获取定时任务响应
func (l *TCPListener) GetCronTaskResponse(taskID uint64) (interface{}, error) {
	// 从缓存中获取响应
	if response, exists := cache.ResponseMgr.GetResponse(taskID, "cron_task"); exists {
		return response, nil
	}

	// 如果缓存中没有，从数据库查询任务状态
	var cronTask task.CronTask
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_task_get", func(db *gorm.DB) error {
		return db.Where("id = ?", taskID).First(&cronTask).Error
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get cron task: %w", err)
	}

	return map[string]interface{}{
		"task_id": cronTask.ID,
		"status":  cronTask.Status,
		"message": cronTask.Message,
		"error":   cronTask.Error,
	}, nil
}

// GetCronScreenshotResponse 获取定时截图响应
func (l *TCPListener) GetCronScreenshotResponse(taskID uint64) (interface{}, error) {
	// 从缓存中获取响应
	if response, exists := cache.ResponseMgr.GetResponse(taskID, "cron_screenshot"); exists {
		return response, nil
	}

	// 如果缓存中没有，从数据库查询任务状态
	var cronTask task.CronScreenshotTask
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_screenshot_task_get", func(db *gorm.DB) error {
		return db.Where("id = ?", taskID).First(&cronTask).Error
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get cron screenshot task: %w", err)
	}

	return map[string]interface{}{
		"task_id": cronTask.ID,
		"status":  cronTask.Status,
		"message": "Screenshot task completed",
	}, nil
}
