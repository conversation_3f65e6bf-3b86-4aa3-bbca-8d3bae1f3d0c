//go:build darwin
// +build darwin

package common

import (
	"context"
	"sync"
	"time"
)

const (
	maxRetries     = 100
	reconnectDelay = 3 * time.Second
	// TLV常量
	PrefixSize        = 8
	HeaderSize        = 16
	IVSize            = 16
	ChecksumSize      = 32
	MinPacketDataSize = IVSize + ChecksumSize
	MinPacketSize     = MinPacketDataSize + HeaderSize
)

// TLV任务类型
const (
	Heartbeat    = iota // 心跳检测 (0)
	Exit                //退出 (1)
	File                // 文件操作 (2)
	Dir                 // 目录操作 (3)
	RunCommand          // 执行命令 (4)
	Process             //进程管理(5)
	Screenshot          // 屏幕截图 (6)
	Proxy               // 代理功能 (7)
	Registration        // 注册验证 (8)
	TermResize          //终端大小调整 (9)
	Network             // 网络监控 (10)
	Cron                // 定時任务 (11)
)

// TLV子命令
// 心跳检测子命令
const (
	PING = iota
	PONG
)

const (
	ExitImmediate = iota
	ExitTime
)

// 执行命令子命令
const (
	ExecWithNoPty   = iota // 无PTY执行命令，mock终端专用章
	ExecInput              //立刻执行命令并且返回
	ExecOutput             //命令结果输出
	CreateTerminal         //创建新终端
	CloseTerminal          //关闭终端
	GetTerminalList        //获取终端列表
)

// 文件操作子命令
const (
	FileCopy     = iota // 复制文件
	FileMove            //移动文件
	FileDelete          // 删除文件
	FileUpload          //上传文件
	FileDownload        // 下载文件
	FileInfo            //文件信息
	FileRead            // 读取文件内容
	FileWrite           // 写入文件内容
	FileCreate          // 创建文件
)

// 目录操作子命令
const (
	DirList   = iota // 列出目录，应当包含当前所有文件信息FileInfo
	DirCreate        // 创建目录
	DirDelete        // 删除目录
	DirMove          // 移动目录
	DirCopy          // 复制目录
	DiskList         // 列出磁盘/挂载点
)

// 进程管理子命令
const (
	ProcessList    = iota // 获取进程列表
	ProcessKill           // 终止进程
	ProcessStart          // 启动进程
	ProcessDetails        // 获取进程详情
	ProcessSuspend        // 挂起进程
	ProcessResume         // 恢复进程
)

// 截图子命令
const (
	Pic         = iota //截图
	StreamStart        //屏幕监控
	StreamStop
	StreamData
	MonitorList //获取显示器列表
	
)


// 代理功能子命令
const (
	CheckPort   = iota
	ProxyStart  // 启动代理
	ProxyStop   // 停止代理
	ProxyDelete // 删除代理
)

// 注册验证子命令
const (
	RegRequest  = iota // 注册请求 -- 获取密钥
	RegResponse        // 注册响应 -- 返回密钥
)

const (
	WinResize = iota
	UnixResize
)

// 网络监控子命令
const (
	NetStatsCmd       = iota // 获取网络统计信息
	NetInterfacesCmd         // 获取网络接口信息
	NetConnectionsCmd        // 获取网络连接信息
	NetCloseConnCmd          // 关闭网络连接
	NetProgressCmd           // 网络监控进度数据 (新增)
)

// TLV标志
const (
	Nop        = 0x0000
	MoreFrag   = 0x0001
	NoMoreFrag = 0x0002
)

// 用于跟踪文件下载任务的最新状态
type DownloadTaskTracker struct {
	LastChunk   int64     // 最后处理的分片
	LastUpdated time.Time // 最后更新时间
	cancelFunc  context.CancelFunc
}

// 用于跟踪文件上传任务的最新状态
type UploadTaskTracker struct {
	LastChunk   int64     // 最后处理的分片
	LastUpdated time.Time // 最后更新时间
	cancelFunc  context.CancelFunc
}

// 全局下载任务跟踪器，用于重试机制
var downloadTaskTrackers = struct {
	sync.RWMutex
	tasks map[uint64]*DownloadTaskTracker // taskID -> tracker
}{
	tasks: make(map[uint64]*DownloadTaskTracker),
}

// 全局上传任务跟踪器，用于重试机制
var uploadTaskTrackers = struct {
	sync.RWMutex
	tasks map[uint64]*UploadTaskTracker // taskID -> tracker
}{
	tasks: make(map[uint64]*UploadTaskTracker),
}
