//go:build linux
// +build linux

package common

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"runtime"
	"github.com/creack/pty"
	"strconv"
	"strings"
	"os"
	"os/exec"
	"sync"
	"time"
)

// TerminalSession 表示一个终端会话
type TerminalSession struct {
	ID            uint32
	ctx           context.Context
	cancel        context.CancelFunc
	pty           *os.File
	cmd           *exec.Cmd
	inputChan     chan []byte
	outputChan    chan []byte
	resizeChan    chan *pty.Winsize
	terminalSize  pty.Winsize
	mutex         sync.Mutex
	isActive      bool
}

// MultiTerminalManager 多终端管理器
type MultiTerminalManager struct {
	cm               *ConnectionManager
	terminals        map[uint32]*TerminalSession
	mutex            sync.RWMutex
	nextBackupID     uint32 // 下一个备用终端ID，从1000开始
	mainTerminalID   uint32 // 主终端ID，固定为0
}

// NewMultiTerminalManager 创建多终端管理器
func NewMultiTerminalManager(cm *ConnectionManager) *MultiTerminalManager {
	return &MultiTerminalManager{
		cm:             cm,
		terminals:      make(map[uint32]*TerminalSession),
		nextBackupID:   1000,
		mainTerminalID: 0,
	}
}

// CreateMainTerminal 创建主终端（ID=0）
func (mtm *MultiTerminalManager) CreateMainTerminal(ctx context.Context) error {
	mtm.mutex.Lock()
	defer mtm.mutex.Unlock()

	// 检查主终端是否已存在
	if _, exists := mtm.terminals[mtm.mainTerminalID]; exists {
		return fmt.Errorf("主终端已存在")
	}

	// 创建主终端会话
	session, err := mtm.createTerminalSession(mtm.mainTerminalID, ctx)
	if err != nil {
		return fmt.Errorf("创建主终端失败: %v", err)
	}

	mtm.terminals[mtm.mainTerminalID] = session
	log.Printf("✅ 主终端创建成功，ID: %d", mtm.mainTerminalID)
	return nil
}

// CreateBackupTerminal 创建备用终端
func (mtm *MultiTerminalManager) CreateBackupTerminal(ctx context.Context) (uint32, error) {
	mtm.mutex.Lock()
	defer mtm.mutex.Unlock()

	terminalID := mtm.nextBackupID
	mtm.nextBackupID++

	// 创建备用终端会话
	session, err := mtm.createTerminalSession(terminalID, ctx)
	if err != nil {
		return 0, fmt.Errorf("创建备用终端失败: %v", err)
	}

	mtm.terminals[terminalID] = session
	log.Printf("✅ 备用终端创建成功，ID: %d", terminalID)
	return terminalID, nil
}

// createTerminalSession 创建终端会话（内部方法）
func (mtm *MultiTerminalManager) createTerminalSession(terminalID uint32, parentCtx context.Context) (*TerminalSession, error) {
	// 创建会话上下文
	ctx, cancel := context.WithCancel(parentCtx)

	// 确定shell类型
	osName := strings.ToLower(runtime.GOOS)
	var shell string
	switch osName {
	case "windows":
		shell = "powershell.exe"
	case "linux":
		shell = "/bin/bash"
	case "darwin":
		shell = "/bin/zsh"
	default:
		shell = "/bin/sh"
	}

	cmd := exec.CommandContext(ctx, shell)
	if osName != "windows" {
		cmd.Env = append(os.Environ(), "TERM=xterm-256color", "LANG=en_US.UTF-8")
	}

	// 创建PTY
	ptmx, err := pty.StartWithSize(cmd, cm.terminalSize.size)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("创建PTY失败: %v", err)
	}


	// 创建会话
	session := &TerminalSession{
		ID:           terminalID,
		ctx:          ctx,
		cancel:       cancel,
		pty:          ptmx,
		cmd:          cmd,
		inputChan:    make(chan []byte, 100),
		outputChan:   make(chan []byte, 1000),
		resizeChan:   make(chan *pty.Winsize, 10),
		terminalSize: pty.Winsize{Cols: 148, Rows: 50},
		isActive:     true,
	}

	log.Printf("✅ 终端%d的PTY命令已启动", terminalID)

	// 启动会话处理goroutines
	go mtm.handleTerminalInput(session)
	go mtm.handleTerminalOutput(session)

	return session, nil
}

// handleTerminalInput 处理终端输入
func (mtm *MultiTerminalManager) handleTerminalInput(session *TerminalSession) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("终端输入处理panic: %v, 终端ID: %d", r, session.ID)
		}
	}()

	for {
		select {
		case <-session.ctx.Done():
			return
		case input, ok := <-session.inputChan:
			if !ok {
				return
			}

			session.mutex.Lock()
			if session.pty != nil && session.isActive {
				if _, err := session.pty.Write(input); err != nil {
					log.Printf("写入PTY失败: %v, 终端ID: %d", err, session.ID)
				}
			}
			session.mutex.Unlock()
		case newSize, ok := <-session.resizeChan:
			if !ok {
				return
			}

			session.mutex.Lock()
			if session.pty != nil && session.isActive {
				if err := pty.Setsize(session.pty, &pty.Winsize{Cols: uint16(newSize.Cols), Rows: uint16(newSize.Rows)} ); err != nil {
					log.Printf("调整终端%d大小失败: %v", session.ID, err)
				} else {
					session.terminalSize = *newSize
					log.Printf("✅ 终端%d大小已调整为: %dx%d", session.ID, newSize.Cols, newSize.Rows)
				}
			}
			session.mutex.Unlock()
		}
	}
}

// handleTerminalOutput 处理终端输出
func (mtm *MultiTerminalManager) handleTerminalOutput(session *TerminalSession) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("终端输出处理panic: %v, 终端ID: %d", r, session.ID)
		}
	}()

	// 启动PTY输出读取
	go func() {
		defer close(session.outputChan)
		
		buffer := make([]byte, 4096)
		for {
			select {
			case <-session.ctx.Done():
				return
			default:
				session.mutex.Lock()
				pty := session.pty
				isActive := session.isActive
				session.mutex.Unlock()
				
				if pty == nil || !isActive {
					return
				}
				
				n, err := pty.Read(buffer)
				if err != nil {
					log.Printf("读取PTY输出失败: %v, 终端ID: %d", err, session.ID)
					return
				}
				
				if n > 0 {
					output := make([]byte, n)
					copy(output, buffer[:n])
					
					select {
					case session.outputChan <- output:
					case <-session.ctx.Done():
						return
					}
				}
			}
		}
	}()

	// 处理输出并发送到服务器
	mtm.sendTerminalOutputToServer(session)
}

// sendTerminalOutputToServer 发送终端输出到服务器
func (mtm *MultiTerminalManager) sendTerminalOutputToServer(session *TerminalSession) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("发送终端输出panic: %v, 终端ID: %d", r, session.ID)
		}
	}()

	buf := make([]byte, 0, 4096)
	flushTimer := time.NewTicker(1 * time.Millisecond)
	defer flushTimer.Stop()

	flush := func() {
		if len(buf) == 0 {
			return
		}

		// 创建CommandResponse结构体
		cmdResponse := &CommandResponse{
			TerminalID: session.ID,
			Output:     string(buf),
			Success:    true,
			Error:      "",
		}

		// 序列化CommandResponse
		responseData, err := cmdResponse.Marshal()
		if err != nil {
			log.Printf("序列化CommandResponse失败: %v", err)
			return
		}

		packets, err := mtm.cm.CreatePackets(responseData, RunCommand, ExecOutput)
		if err != nil {
			log.Printf("创建输出包失败: %v", err)
			return
		}

		// 批量发送所有包
		for _, packet := range packets {
			packetBytes := packet.Serialize()
			if _, err = mtm.cm.conn.Write(packetBytes); err != nil {
				log.Printf("发送输出失败: %v", err)
				return
			}
		}

		log.Printf("⏱️ 发送终端%d输出，数据大小: %d", session.ID, len(buf))
		buf = buf[:0] // 重置缓冲区
	}

	for {
		select {
		case <-session.ctx.Done():
			flush()
			return

		case <-flushTimer.C:
			flush()

		case output, ok := <-session.outputChan:
			if !ok {
				flush()
				return
			}

			buf = append(buf, output...)

			// 检查是否需要立即刷新
			shouldFlush := len(buf) > 512 ||
				bytes.Contains(output, []byte{'\n'}) ||
				bytes.Contains(output, []byte{'\r'}) ||
				bytes.Contains(output, []byte{'\x1b'}) // ANSI转义序列

			if shouldFlush {
				flush()
			}
		}
	}
}

// SendCommand 向指定终端发送命令
func (mtm *MultiTerminalManager) SendCommand(terminalID uint32, command string) error {
	mtm.mutex.RLock()
	session, exists := mtm.terminals[terminalID]
	mtm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("终端 %d 不存在", terminalID)
	}

	// 检查是否是RESIZE命令
	if strings.HasPrefix(command, "RESIZE:") {
		return mtm.handleResizeCommand(session, command)
	}

	select {
	case session.inputChan <- []byte(command):
		return nil
	case <-session.ctx.Done():
		return fmt.Errorf("终端 %d 已关闭", terminalID)
	default:
		return fmt.Errorf("终端 %d 输入缓冲区已满", terminalID)
	}
}

// handleResizeCommand 处理终端大小调整命令
func (mtm *MultiTerminalManager) handleResizeCommand(session *TerminalSession, command string) error {
	// 解析RESIZE命令: RESIZE:cols:rows
	parts := strings.Split(command, ":")
	if len(parts) != 3 {
		return fmt.Errorf("无效的RESIZE命令格式: %s", command)
	}

	cols, err := strconv.ParseUint(parts[1], 10, 16)
	if err != nil {
		return fmt.Errorf("无效的列数: %s", parts[1])
	}

	rows, err := strconv.ParseUint(parts[2], 10, 16)
	if err != nil {
		return fmt.Errorf("无效的行数: %s", parts[2])
	}

	// 创建新的终端大小
	newSize := &pty.Winsize{
		Cols: uint16(cols),
		Rows: uint16(rows),
	}

	// 发送到resize通道
	select {
	case session.resizeChan <- newSize:
		log.Printf("✅ 终端%d大小调整: %dx%d", session.ID, cols, rows)
		return nil
	case <-session.ctx.Done():
		return fmt.Errorf("终端 %d 已关闭", session.ID)
	default:
		return fmt.Errorf("终端 %d resize缓冲区已满", session.ID)
	}
}

// CloseTerminal 关闭指定终端
func (mtm *MultiTerminalManager) CloseTerminal(terminalID uint32) error {
	mtm.mutex.Lock()
	defer mtm.mutex.Unlock()

	session, exists := mtm.terminals[terminalID]
	if !exists {
		return fmt.Errorf("终端 %d 不存在", terminalID)
	}

	// 不允许关闭主终端
	if terminalID == mtm.mainTerminalID {
		return fmt.Errorf("不能关闭主终端")
	}

	// 关闭会话
	session.mutex.Lock()
	session.isActive = false
	if session.pty != nil {
		session.pty.Close()
	}
	session.cancel()
	session.mutex.Unlock()

	// 从管理器中移除
	delete(mtm.terminals, terminalID)
	
	log.Printf("✅ 终端关闭成功，ID: %d", terminalID)
	return nil
}


// GetTerminalList 获取终端列表
func (mtm *MultiTerminalManager) GetTerminalList() []uint32 {
	mtm.mutex.RLock()
	defer mtm.mutex.RUnlock()

	var terminals []uint32
	for id := range mtm.terminals {
		terminals = append(terminals, id)
	}
	return terminals
}

// IsMainTerminal 检查是否为主终端
func (mtm *MultiTerminalManager) IsMainTerminal(terminalID uint32) bool {
	return terminalID == mtm.mainTerminalID
}

// Cleanup 清理所有终端
func (mtm *MultiTerminalManager) Cleanup() {
	mtm.mutex.Lock()
	defer mtm.mutex.Unlock()

	for id, session := range mtm.terminals {
		session.mutex.Lock()
		session.isActive = false
		if session.pty != nil {
			session.pty.Close()
		}
		session.cancel()
		session.mutex.Unlock()
		
		log.Printf("清理终端，ID: %d", id)
	}

	mtm.terminals = make(map[uint32]*TerminalSession)
	log.Println("✅ 所有终端已清理")
}
