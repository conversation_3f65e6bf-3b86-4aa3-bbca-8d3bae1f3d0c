package cron

import (
	"context"
	"encoding/json"
	"fmt"
	"server/core/manager/cache"
	"server/core/manager/dbpool"
	"server/core/manager/workerpool"
	"server/global"
	"server/model/basic"
	"server/model/task"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CronManager 定时任务管理器
type CronManager struct {
	cron     *cron.Cron
	jobs     map[uint]*CronJobWrapper
	mu       sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	running  bool
}

// CronJobWrapper 定时任务包装器
type CronJobWrapper struct {
	Job    *basic.CronJob
	EntryID cron.EntryID
	Config  interface{}
}

// GlobalCronManager 全局定时任务管理器
var GlobalCronManager *CronManager

// init 初始化全局定时任务管理器
func init() {
	GlobalCronManager = NewCronManager()
}

// NewCronManager 创建新的定时任务管理器
func NewCronManager() *CronManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 创建cron实例，使用秒级精度
	c := cron.New(cron.WithSeconds())
	
	return &CronManager{
		cron:   c,
		jobs:   make(map[uint]*CronJobWrapper),
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动定时任务管理器
func (cm *CronManager) Start() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	if cm.running {
		return fmt.Errorf("cron manager is already running")
	}
	
	// 启动cron调度器
	cm.cron.Start()
	cm.running = true
	
	// 加载现有的定时任务
	go cm.loadExistingJobs()
	
	global.LOG.Info("Cron manager started successfully")
	return nil
}

// Stop 停止定时任务管理器
func (cm *CronManager) Stop() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	if !cm.running {
		return fmt.Errorf("cron manager is not running")
	}
	
	// 停止cron调度器
	ctx := cm.cron.Stop()
	<-ctx.Done()
	
	// 取消上下文
	cm.cancel()
	cm.running = false
	
	global.LOG.Info("Cron manager stopped successfully")
	return nil
}

// AddJob 添加定时任务
func (cm *CronManager) AddJob(job *basic.CronJob) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	// 解析配置
	config, err := cm.parseJobConfig(job)
	if err != nil {
		return fmt.Errorf("failed to parse job config: %w", err)
	}
	
	// 添加到cron调度器
	entryID, err := cm.cron.AddFunc(job.CronExpr, func() {
		cm.executeJob(job, config)
	})
	if err != nil {
		return fmt.Errorf("failed to add job to cron: %w", err)
	}
	
	// 保存到内存
	cm.jobs[job.ID] = &CronJobWrapper{
		Job:     job,
		EntryID: entryID,
		Config:  config,
	}
	
	// 更新下次执行时间
	cm.updateNextExecTime(job.ID)
	
	global.LOG.Info("Cron job added successfully", 
		zap.Uint("job_id", job.ID), 
		zap.String("name", job.Name),
		zap.String("cron_expr", job.CronExpr))
	
	return nil
}

// RemoveJob 移除定时任务
func (cm *CronManager) RemoveJob(jobID uint) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	
	wrapper, exists := cm.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found: %d", jobID)
	}
	
	// 从cron调度器中移除
	cm.cron.Remove(wrapper.EntryID)
	
	// 从内存中移除
	delete(cm.jobs, jobID)
	
	global.LOG.Info("Cron job removed successfully", zap.Uint("job_id", jobID))
	return nil
}

// UpdateJob 更新定时任务
func (cm *CronManager) UpdateJob(job *basic.CronJob) error {
	// 先移除旧任务
	if err := cm.RemoveJob(job.ID); err != nil {
		// 如果任务不存在，忽略错误
		global.LOG.Warn("Failed to remove old job", zap.Error(err))
	}
	
	// 添加新任务
	return cm.AddJob(job)
}

// PauseJob 暂停定时任务
func (cm *CronManager) PauseJob(jobID uint) error {
	return cm.RemoveJob(jobID)
}

// ResumeJob 恢复定时任务
func (cm *CronManager) ResumeJob(jobID uint) error {
	// 从数据库重新加载任务
	var job basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_load", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = 'active'", jobID).First(&job).Error
	})
	if err != nil {
		return fmt.Errorf("failed to load job from database: %w", err)
	}
	
	return cm.AddJob(&job)
}

// GetJobStatus 获取任务状态
func (cm *CronManager) GetJobStatus(jobID uint) (string, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	wrapper, exists := cm.jobs[jobID]
	if !exists {
		return "stopped", nil
	}
	
	return wrapper.Job.Status, nil
}

// GetJobList 获取任务列表
func (cm *CronManager) GetJobList() []*basic.CronJob {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	jobs := make([]*basic.CronJob, 0, len(cm.jobs))
	for _, wrapper := range cm.jobs {
		jobs = append(jobs, wrapper.Job)
	}
	
	return jobs
}

// loadExistingJobs 加载现有的定时任务
func (cm *CronManager) loadExistingJobs() {
	var jobs []basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_jobs_load", func(db *gorm.DB) error {
		return db.Where("status = 'active'").Find(&jobs).Error
	})
	if err != nil {
		global.LOG.Error("Failed to load existing cron jobs", zap.Error(err))
		return
	}
	
	for _, job := range jobs {
		if err := cm.AddJob(&job); err != nil {
			global.LOG.Error("Failed to add existing cron job", 
				zap.Uint("job_id", job.ID), 
				zap.Error(err))
		}
	}
	
	global.LOG.Info("Loaded existing cron jobs", zap.Int("count", len(jobs)))
}

// parseJobConfig 解析任务配置
func (cm *CronManager) parseJobConfig(job *basic.CronJob) (interface{}, error) {
	switch job.TaskType {
	case "screenshot":
		var config basic.ScreenshotCronConfig
		if err := json.Unmarshal([]byte(job.Config), &config); err != nil {
			return nil, err
		}
		return config, nil
	default:
		return nil, fmt.Errorf("unsupported task type: %s", job.TaskType)
	}
}

// executeJob 执行定时任务
func (cm *CronManager) executeJob(job *basic.CronJob, config interface{}) {
	// 🚀 使用工作池执行任务
	workerpool.NewGeneralTask(fmt.Sprintf("cron_job_%d", job.ID), func() error {
		return cm.doExecuteJob(job, config)
	}).Submit()
}

// doExecuteJob 实际执行定时任务
func (cm *CronManager) doExecuteJob(job *basic.CronJob, config interface{}) error {
	startTime := time.Now()
	
	// 创建执行记录
	execution := &basic.CronJobExecution{
		CronJobID: job.ID,
		Status:    "running",
		StartedAt: &startTime,
	}
	
	// 保存执行记录
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_execution_create", func(db *gorm.DB) error {
		return db.Create(execution).Error
	})
	if err != nil {
		global.LOG.Error("Failed to create cron execution record", zap.Error(err))
		return err
	}
	
	// 执行具体任务
	var taskID uint64
	var execErr error
	
	switch job.TaskType {
	case "screenshot":
		taskID, execErr = cm.executeScreenshotJob(job, config.(basic.ScreenshotCronConfig))
	default:
		execErr = fmt.Errorf("unsupported task type: %s", job.TaskType)
	}
	
	// 更新执行记录
	endTime := time.Now()
	duration := endTime.Sub(startTime).Milliseconds()
	
	execution.TaskID = taskID
	execution.EndedAt = &endTime
	execution.Duration = duration
	
	if execErr != nil {
		execution.Status = "failed"
		execution.Error = execErr.Error()
	} else {
		execution.Status = "success"
	}
	
	// 保存执行结果
	err = dbpool.ExecuteDBOperationAsyncAndWait("cron_execution_update", func(db *gorm.DB) error {
		return db.Save(execution).Error
	})
	if err != nil {
		global.LOG.Error("Failed to update cron execution record", zap.Error(err))
	}
	
	// 更新任务执行次数和最后执行时间
	cm.updateJobExecution(job.ID, startTime)
	
	// 检查是否达到最大执行次数
	if job.MaxCount > 0 && job.ExecCount >= job.MaxCount {
		cm.stopJobDueToMaxCount(job.ID)
	}
	
	return execErr
}

// executeScreenshotJob 执行定时截图任务
func (cm *CronManager) executeScreenshotJob(job *basic.CronJob, config basic.ScreenshotCronConfig) (uint64, error) {
	// 创建截图任务
	screenshotTask := &task.CronScreenshotTask{
		CronJobID: job.ID,
		ClientID:  job.ClientID,
		TaskType:  "cron_screenshot",
		Status:    "pending",
		Quality:   config.Quality,
		Format:    config.Format,
		MonitorID: config.MonitorID,
		Type:      config.Type,
	}

	// 保存任务到数据库
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_screenshot_task_create", func(db *gorm.DB) error {
		return db.Create(screenshotTask).Error
	})
	if err != nil {
		return 0, fmt.Errorf("failed to create screenshot task: %w", err)
	}

	// 发送任务到客户端
	err = cm.sendTaskToClient(job.ClientID, screenshotTask)
	if err != nil {
		return screenshotTask.ID, fmt.Errorf("failed to send task to client: %w", err)
	}

	return screenshotTask.ID, nil
}

// sendTaskToClient 发送任务到客户端
func (cm *CronManager) sendTaskToClient(clientID uint, task interface{}) error {
	// 这里需要实现发送任务到客户端的逻辑
	// 暂时返回nil，具体实现在后续的listener中完成
	return nil
}

// updateJobExecution 更新任务执行信息
func (cm *CronManager) updateJobExecution(jobID uint, execTime time.Time) {
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_update_exec", func(db *gorm.DB) error {
		return db.Model(&basic.CronJob{}).Where("id = ?", jobID).Updates(map[string]interface{}{
			"exec_count":   gorm.Expr("exec_count + 1"),
			"last_exec_at": execTime,
		}).Error
	})
	if err != nil {
		global.LOG.Error("Failed to update job execution info",
			zap.Uint("job_id", jobID),
			zap.Error(err))
	}

	// 更新内存中的任务信息
	cm.mu.Lock()
	if wrapper, exists := cm.jobs[jobID]; exists {
		wrapper.Job.ExecCount++
		wrapper.Job.LastExecAt = &execTime
	}
	cm.mu.Unlock()

	// 更新下次执行时间
	cm.updateNextExecTime(jobID)
}

// updateNextExecTime 更新下次执行时间
func (cm *CronManager) updateNextExecTime(jobID uint) {
	cm.mu.RLock()
	wrapper, exists := cm.jobs[jobID]
	cm.mu.RUnlock()

	if !exists {
		return
	}

	// 获取下次执行时间
	entry := cm.cron.Entry(wrapper.EntryID)
	if !entry.Next.IsZero() {
		nextExecTime := entry.Next

		// 更新数据库
		err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_update_next", func(db *gorm.DB) error {
			return db.Model(&basic.CronJob{}).Where("id = ?", jobID).Update("next_exec_at", nextExecTime).Error
		})
		if err != nil {
			global.LOG.Error("Failed to update next execution time",
				zap.Uint("job_id", jobID),
				zap.Error(err))
		}

		// 更新内存
		cm.mu.Lock()
		if wrapper, exists := cm.jobs[jobID]; exists {
			wrapper.Job.NextExecAt = &nextExecTime
		}
		cm.mu.Unlock()
	}
}

// stopJobDueToMaxCount 因达到最大执行次数而停止任务
func (cm *CronManager) stopJobDueToMaxCount(jobID uint) {
	// 更新任务状态为停止
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_stop", func(db *gorm.DB) error {
		return db.Model(&basic.CronJob{}).Where("id = ?", jobID).Update("status", "stopped").Error
	})
	if err != nil {
		global.LOG.Error("Failed to stop job due to max count",
			zap.Uint("job_id", jobID),
			zap.Error(err))
		return
	}

	// 从调度器中移除
	if err := cm.RemoveJob(jobID); err != nil {
		global.LOG.Error("Failed to remove job from scheduler",
			zap.Uint("job_id", jobID),
			zap.Error(err))
	}

	global.LOG.Info("Job stopped due to max count reached", zap.Uint("job_id", jobID))
}

// GetStats 获取统计信息
func (cm *CronManager) GetStats() map[string]interface{} {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	stats := map[string]interface{}{
		"total_jobs":   len(cm.jobs),
		"running":      cm.running,
		"cron_entries": len(cm.cron.Entries()),
	}

	// 统计各状态的任务数量
	statusCount := make(map[string]int)
	for _, wrapper := range cm.jobs {
		statusCount[wrapper.Job.Status]++
	}
	stats["status_count"] = statusCount

	return stats
}
