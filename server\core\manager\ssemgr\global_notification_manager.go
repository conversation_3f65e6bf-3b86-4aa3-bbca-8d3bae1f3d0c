package ssemgr

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"server/global"
	"server/model/response"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// NotificationSSEConnection SSE连接信息
type NotificationSSEConnection struct {
	UserID     uint                              `json:"userId"`
	Writer     gin.ResponseWriter                `json:"-"`
	Request    *http.Request                     `json:"-"`
	Context    context.Context                   `json:"-"`
	Cancel     context.CancelFunc                `json:"-"`
	LastPing   time.Time                         `json:"lastPing"`
	CreatedAt  time.Time                         `json:"createdAt"`
	RemoteAddr string                            `json:"remoteAddr"`
	UserAgent  string                            `json:"userAgent"`
	Channel    chan response.NotificationSSEData `json:"-"`
}

// NotificationSSEManager SSE连接管理器
type NotificationSSEManager struct {
	connections map[uint]*NotificationSSEConnection // userID -> connection
	mutex       sync.RWMutex
	pingTicker  *time.Ticker
	stopChan    chan struct{}
}

// NewNotificationSSEManager 创建SSE管理器
func NewNotificationSSEManager() *NotificationSSEManager {
	manager := &NotificationSSEManager{
		connections: make(map[uint]*NotificationSSEConnection),
		pingTicker:  time.NewTicker(30 * time.Second), // 30秒心跳
		stopChan:    make(chan struct{}),
	}

	// 启动心跳检测
	go manager.startHeartbeat()

	return manager
}

// AddConnection 添加SSE连接
func (m *NotificationSSEManager) AddConnection(userID uint, w gin.ResponseWriter, r *http.Request) *NotificationSSEConnection {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 如果用户已有连接，先关闭旧连接
	if oldConn, exists := m.connections[userID]; exists {
		global.LOG.Info("关闭用户旧的SSE连接", zap.Uint("userID", userID))
		oldConn.Cancel()
		close(oldConn.Channel)
	}

	// 创建新连接
	ctx, cancel := context.WithCancel(r.Context())
	conn := &NotificationSSEConnection{
		UserID:     userID,
		Writer:     w,
		Request:    r,
		Context:    ctx,
		Cancel:     cancel,
		LastPing:   time.Now(),
		CreatedAt:  time.Now(),
		RemoteAddr: r.RemoteAddr,
		UserAgent:  r.UserAgent(),
		Channel:    make(chan response.NotificationSSEData, 100), // 缓冲100条消息
	}

	m.connections[userID] = conn

	global.LOG.Info("新增SSE连接",
		zap.Uint("userID", userID),
		zap.String("remoteAddr", conn.RemoteAddr),
		zap.Int("totalConnections", len(m.connections)))

	return conn
}

// RemoveConnection 移除SSE连接
func (m *NotificationSSEManager) RemoveConnection(userID uint) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if conn, exists := m.connections[userID]; exists {
		// 取消上下文
		conn.Cancel()

		// 安全关闭channel
		select {
		case <-conn.Channel:
			// channel已经关闭或为空
		default:
			// 尝试关闭channel
			defer func() {
				if r := recover(); r != nil {
					global.LOG.Warn("关闭SSE channel时发生panic",
						zap.Uint("userID", userID),
						zap.Any("panic", r))
				}
			}()
			close(conn.Channel)
		}

		delete(m.connections, userID)

		global.LOG.Info("移除SSE连接",
			zap.Uint("userID", userID),
			zap.Int("remainingConnections", len(m.connections)))
	}
}

// GetConnection 获取用户连接
func (m *NotificationSSEManager) GetConnection(userID uint) (*NotificationSSEConnection, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	conn, exists := m.connections[userID]
	return conn, exists
}

// SendToUser 发送消息给指定用户
func (m *NotificationSSEManager) SendToUser(userID uint, data response.NotificationSSEData) error {
	conn, exists := m.GetConnection(userID)
	if !exists {
		return fmt.Errorf("用户 %d 的SSE连接不存在", userID)
	}

	select {
	case conn.Channel <- data:
		global.LOG.Debug("SSE消息已发送到用户队列",
			zap.Uint("userID", userID),
			zap.String("type", data.Type),
			zap.String("action", data.Action))
		return nil
	case <-time.After(5 * time.Second):
		global.LOG.Warn("SSE消息发送超时，移除连接",
			zap.Uint("userID", userID))
		m.RemoveConnection(userID)
		return fmt.Errorf("发送消息超时")
	}
}

// SendToMultipleUsers 发送消息给多个用户
func (m *NotificationSSEManager) SendToMultipleUsers(userIDs []uint, data response.NotificationSSEData) {
	for _, userID := range userIDs {
		if err := m.SendToUser(userID, data); err != nil {
			global.LOG.Warn("发送SSE消息失败",
				zap.Uint("userID", userID),
				zap.Error(err))
		}
	}
}

// BroadcastToAll 广播消息给所有连接的用户
func (m *NotificationSSEManager) BroadcastToAll(data response.NotificationSSEData) {
	m.mutex.RLock()
	userIDs := make([]uint, 0, len(m.connections))
	for userID := range m.connections {
		userIDs = append(userIDs, userID)
	}
	m.mutex.RUnlock()

	m.SendToMultipleUsers(userIDs, data)
}

// HandleSSEConnection 处理SSE连接
func (m *NotificationSSEManager) HandleSSEConnection(conn *NotificationSSEConnection) {
	// 确保在函数退出时清理连接
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("SSE连接处理发生panic",
				zap.Uint("userID", conn.UserID),
				zap.Any("panic", r))
		}
		m.RemoveConnection(conn.UserID)
	}()

	// 设置SSE响应头（只有在还没设置的情况下才设置）
	if conn.Writer.Header().Get("Content-Type") == "" {
		conn.Writer.Header().Set("Content-Type", "text/event-stream")
		conn.Writer.Header().Set("Cache-Control", "no-cache")
		conn.Writer.Header().Set("Connection", "keep-alive")
		conn.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		conn.Writer.Header().Set("Access-Control-Allow-Headers", "Cache-Control")
	}

	// 确保响应头被发送
	if flusher, ok := conn.Writer.(http.Flusher); ok {
		flusher.Flush()
	}

	// 发送连接成功消息
	if err := m.sendSSEMessage(conn, "connected", map[string]interface{}{
		"message":   "SSE连接已建立",
		"timestamp": time.Now(),
		"userID":    conn.UserID,
	}); err != nil {
		global.LOG.Error("发送连接成功消息失败",
			zap.Uint("userID", conn.UserID),
			zap.Error(err))
		return
	}

	// 处理消息循环
	for {
		select {
		case <-conn.Context.Done():
			global.LOG.Info("SSE连接上下文已取消", zap.Uint("userID", conn.UserID))
			return

		case data, ok := <-conn.Channel:
			if !ok {
				global.LOG.Info("SSE连接通道已关闭", zap.Uint("userID", conn.UserID))
				return
			}

			// 发送通知数据
			if err := m.sendSSEMessage(conn, "notification", data); err != nil {
				global.LOG.Error("发送SSE消息失败",
					zap.Uint("userID", conn.UserID),
					zap.Error(err))
				return
			}

		case <-time.After(30 * time.Second):
			// 发送心跳
			if err := m.sendSSEMessage(conn, "ping", map[string]interface{}{
				"timestamp": time.Now(),
			}); err != nil {
				global.LOG.Error("发送SSE心跳失败",
					zap.Uint("userID", conn.UserID),
					zap.Error(err))
				return
			}
			conn.LastPing = time.Now()
		}
	}
}

// sendSSEMessage 发送SSE消息
func (m *NotificationSSEManager) sendSSEMessage(conn *NotificationSSEConnection, event string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 确保消息格式正确，避免chunked编码问题
	message := fmt.Sprintf("event: %s\ndata: %s\n\n", event, string(jsonData))

	// 检查连接是否仍然有效
	select {
	case <-conn.Context.Done():
		return fmt.Errorf("连接已关闭")
	default:
	}

	// 写入数据
	n, err := conn.Writer.Write([]byte(message))
	if err != nil {
		return fmt.Errorf("写入响应失败: %v", err)
	}

	// 确保数据完整写入
	if n != len(message) {
		return fmt.Errorf("数据写入不完整: 期望 %d 字节，实际写入 %d 字节", len(message), n)
	}

	// 立即刷新缓冲区
	if flusher, ok := conn.Writer.(http.Flusher); ok {
		flusher.Flush()
	} else {
		global.LOG.Warn("Writer不支持Flush操作", zap.Uint("userID", conn.UserID))
	}

	return nil
}

// startHeartbeat 启动心跳检测
func (m *NotificationSSEManager) startHeartbeat() {
	for {
		select {
		case <-m.pingTicker.C:
			m.checkConnections()
		case <-m.stopChan:
			return
		}
	}
}

// checkConnections 检查连接状态
func (m *NotificationSSEManager) checkConnections() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	timeoutDuration := 2 * time.Minute // 2分钟超时

	for userID, conn := range m.connections {
		if now.Sub(conn.LastPing) > timeoutDuration {
			global.LOG.Info("SSE连接超时，移除连接",
				zap.Uint("userID", userID),
				zap.Duration("timeout", now.Sub(conn.LastPing)))

			conn.Cancel()
			close(conn.Channel)
			delete(m.connections, userID)
		}
	}
}

// GetStats 获取连接统计信息
func (m *NotificationSSEManager) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return map[string]interface{}{
		"totalConnections": len(m.connections),
		"connections":      m.getAllConnectionInfo(),
	}
}

// getAllConnectionInfo 获取所有连接信息
func (m *NotificationSSEManager) getAllConnectionInfo() []map[string]interface{} {
	connections := make([]map[string]interface{}, 0, len(m.connections))

	for userID, conn := range m.connections {
		connections = append(connections, map[string]interface{}{
			"userID":     userID,
			"remoteAddr": conn.RemoteAddr,
			"userAgent":  conn.UserAgent,
			"createdAt":  conn.CreatedAt,
			"lastPing":   conn.LastPing,
			"duration":   time.Since(conn.CreatedAt).String(),
		})
	}

	return connections
}

// Stop 停止SSE管理器
func (m *NotificationSSEManager) Stop() {
	close(m.stopChan)
	m.pingTicker.Stop()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭所有连接
	for userID, conn := range m.connections {
		conn.Cancel()
		close(conn.Channel)
		global.LOG.Info("关闭SSE连接", zap.Uint("userID", userID))
	}

	m.connections = make(map[uint]*NotificationSSEConnection)
}

// 全局SSE管理器实例
var GlobalNotificationSSEManager *NotificationSSEManager

// InitNotificationSSEManager 初始化全局SSE管理器
func InitNotificationSSEManager() {
	GlobalNotificationSSEManager = NewNotificationSSEManager()
	global.LOG.Info("SSE管理器初始化完成")
}
