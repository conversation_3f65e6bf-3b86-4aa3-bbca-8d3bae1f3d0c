package task

import (
	"time"
)

// CronTask 定时任务模型
type CronTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	CronJobID   uint       `json:"cron_job_id" gorm:"not null;index"`
	ClientID    uint       `json:"client_id" gorm:"not null;index"`
	TaskType    string     `json:"task_type" gorm:"size:50;not null"` // create_cron, update_cron, delete_cron, execute_cron
	Status      string     `json:"status" gorm:"size:20;default:'pending'"`
	Error       string     `json:"error" gorm:"type:text"`
	Config      string     `json:"config" gorm:"type:text"` // JSON配置
	Result      string     `json:"result" gorm:"type:text"` // 执行结果
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
}

// TableName 指定表名
func (CronTask) TableName() string {
	return "cron_tasks"
}

// CronScreenshotTask 定时截图任务模型
type CronScreenshotTask struct {
	ID          uint64     `json:"id" gorm:"primarykey"`
	CronJobID   uint       `json:"cron_job_id" gorm:"not null;index"`
	ClientID    uint       `json:"client_id" gorm:"not null;index"`
	TaskType    string     `json:"task_type" gorm:"size:50;not null"` // cron_screenshot
	Status      string     `json:"status" gorm:"size:20;default:'pending'"`
	Error       string     `json:"error" gorm:"type:text"`
	Quality     int        `json:"quality" gorm:"default:80"`
	Format      string     `json:"format" gorm:"size:10;default:'png'"`
	MonitorID   int        `json:"monitor_id" gorm:"default:0"`
	Type        int        `json:"type" gorm:"default:0"` // 0=全屏, 1=活动窗口, 2=指定区域
	Filename    string     `json:"filename" gorm:"size:255"`
	FilePath    string     `json:"file_path" gorm:"size:500"`
	FileSize    int64      `json:"file_size" gorm:"default:0"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
}

// TableName 指定表名
func (CronScreenshotTask) TableName() string {
	return "cron_screenshot_tasks"
}
