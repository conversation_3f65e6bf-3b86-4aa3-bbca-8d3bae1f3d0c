package c2

import (
	"server/api/c2"

	"github.com/gin-gonic/gin"
)

type CronRouter struct{}

func (r *CronRouter) InitCronRouter(Router *gin.RouterGroup) {
	cronRouter := Router.Group("cron")
	cronApi := c2.NewCronApi()
	{
		// 定时任务管理
		cronRouter.POST("/:client_id/create", cronApi.CreateCronJob)           // 创建定时任务
		cronRouter.POST("/:client_id/screenshot", cronApi.CreateScreenshotCron) // 创建定时截图任务
		cronRouter.PUT("/:client_id/update", cronApi.UpdateCronJob)            // 更新定时任务
		cronRouter.DELETE("/:client_id/delete/:job_id", cronApi.DeleteCronJob) // 删除定时任务
		cronRouter.PUT("/:client_id/status", cronApi.UpdateCronJobStatus)      // 更新定时任务状态
		cronRouter.POST("/:client_id/execute", cronApi.ExecuteCronJob)         // 手动执行定时任务
		
		// 查询接口
		cronRouter.POST("/list", cronApi.GetCronJobList)                       // 获取定时任务列表
		cronRouter.POST("/executions", cronApi.GetCronJobExecutionList)        // 获取定时任务执行记录列表
	}
}
