package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type ScreenshotRoute struct{}

// InitScreenshotRoute 初始化截图路由
func (s *ScreenshotRoute) InitScreenshotRoute(Router *gin.RouterGroup) {
	screenshotRouter := Router.Group("screenshot").Use(middleware.OperationRecord())
	{
		// 截图操作
		screenshotRouter.POST("/take/:clientId", screenshotApi.TakeScreenshot)
		screenshotRouter.POST("/list/:clientId", screenshotApi.GetScreenshotList)
		screenshotRouter.DELETE("/delete/:clientId/:filename", screenshotApi.DeleteScreenshot)
		// 屏幕流操作
		screenshotRouter.POST("/stream/start/:clientId", screenshotApi.StartScreenStream)
		screenshotRouter.POST("/stream/stop/:clientId", screenshotApi.StopScreenStream)
		screenshotRouter.GET("/stream/data/:clientId", screenshotApi.GetStreamData)
		// 获取显示器列表
		screenshotRouter.GET("/monitors/:clientId", screenshotApi.GetMonitorList)
		// 获取所有截图（不限制客户端）
		screenshotRouter.POST("/all", screenshotApi.GetAllScreenshots)
		// 根据截图ID删除截图
		screenshotRouter.DELETE("/deleteById/:screenshotId", screenshotApi.DeleteScreenshotById)

	}
}
